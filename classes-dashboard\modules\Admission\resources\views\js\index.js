let columns = [
    {
        data: 'action',
        name: 'action',
        orderable: false
    },
    {
        data: 'photo',
        name: 'photo',
        orderable: false
    },
    {
        data: 'student_full_name',
        name: 'student_full_name',
    },
    {
        data: 'contact_no',
        name: 'contact_no',
    },
    {
        data: 'year_name',
        name: 'year_name',
    },
    {
        data: 'class_info',
        name: 'class_info',
    },
];

let data = function(d) {


    d.year = $('#year_filter').val()
        d.department = $('#department').val()
        d.classroom = $('#classroom-filter').val()
        d.status = $('#status-filter').val()
}

let table = commonDatatable('#admissions_table', admissionRoute.index, columns, data);

function tablescroll() {
    $('html, body').animate({
        scrollTop: $("#admissions_table").offset().top
    }, 1000, );
}

$("#filter").on('click', function(event) {
    event.preventDefault();
    tablescroll();
    table.draw();
});

$('#filterreset').click(function() {
    event.preventDefault();
    $('#year_filter').val("").trigger('change');
    $('#department').val("").trigger('change');
    $('#classroom-filter').val("").trigger('change');
    $('#status-filter').val("").trigger('change');
    tablescroll();
    table.draw();
});

$(document).on("click", ".exportData", function () {
    var url = admissionRoute.export;
    var data = {
        year : $('#year_filter').val(),
        department : $('#department').val(),
        classroom : $('#classroom-filter').val(),
        status : $('#status-filter').val(),
    };

    exportData(url, data);
});

// Function to handle student photo loading fallback
function loadStudentPhotoFallback(imgElement) {
    // If the image fails to load, show a placeholder with student initials
    var studentId = imgElement.getAttribute('data-student-id');
    var studentName = imgElement.getAttribute('title') || 'Student';

    // Extract initials from student name
    var initials = 'NA';
    if (studentName && studentName !== 'Student') {
        var nameParts = studentName.trim().split(' ');
        if (nameParts.length >= 2) {
            initials = nameParts[0].charAt(0).toUpperCase() + nameParts[nameParts.length - 1].charAt(0).toUpperCase();
        } else if (nameParts.length === 1) {
            initials = nameParts[0].charAt(0).toUpperCase();
        }
    }

    // Set fallback placeholder image
    imgElement.src = 'https://via.placeholder.com/40x40/ffebee/d32f2f?text=' + initials;
    imgElement.alt = 'Photo not available';
}
